import React from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { <PERSON><PERSON><PERSON>rumbs, Button, IconButton, Typography } from "@apollo/ui"
import { Appstore, Folder, Heart, History } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * A breadcrumb is a list of links that helps visualize a page's location
 * within a site's hierarchical structure. It allows navigation up to any of the ancestors.
 */
const meta: Meta<typeof Breadcrumbs> = {
  title: "@apollo∕ui/Components/Navigation/Breadcrumbs",
  component: Breadcrumbs,
  tags: ["autodocs"],
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: "centered",
    docs: {
      description: {
        component:
          "A breadcrumb is a list of links that helps visualize a page's location within a site's hierarchical structure. It allows navigation up to any of the ancestors.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Breadcrumbs } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="breadcrumbs-props">Props</h2>
          <ArgTypes />
          <h2 id="breadcrumbs-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Breadcrumbs show users their current location relative to the information architecture and enable them to quickly move up to a parent level or previous step",
              "Place breadcrumbs near the top of the page, typically below the main navigation",
              "Keep breadcrumb labels concise and descriptive of the page or section",
              "Use the last item as non-interactive text to represent the current page",
              "Consider using ellipsis (maxItems) for deep navigation hierarchies to save space",
            ]}
          />
          <h2 id="breadcrumbs-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide an <code>aria-label</code> attribute on the
                breadcrumbs navigation element to identify it as a breadcrumb
                trail.
              </>,
              <>
                Use <code>maxItems</code> to limit the number of visible items to maintain readability and avoid layout issues.
              </>,
              <>
                The current page should be represented as non-interactive text
                (typically using <code>Typography</code> component) rather than
                a link.
              </>,
              <>
                Use <code>aria-disabled</code> attribute for disabled breadcrumb
                items to communicate their state to assistive technologies.
              </>,
            ]}
          />
          <h2 id="breadcrumbs-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Breadcrumbs component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloBreadcrumbs-root",
                description: "Styles applied to the breadcrumbs root container",
                usageNotes:
                  "Use for overall breadcrumbs styling and width control",
              },
              {
                cssClassName: ".ApolloBreadcrumbs-list",
                description: "Styles applied to the breadcrumbs list container",
                usageNotes:
                  "Contains flex layout and gap spacing for breadcrumb items",
              },
              {
                cssClassName: ".ApolloBreadcrumbs-item",
                description: "Styles applied to individual breadcrumb items",
                usageNotes: "Contains padding, hover states, and item styling",
              },
              {
                cssClassName: ".ApolloBreadcrumbs-ellipsis",
                description: "Styles applied to the ellipsis item",
                usageNotes: "Contains ellipsis button and click handler",
              },
            ]}
          />
          <h2 id="breadcrumbs-examples">Examples</h2>
          <Stories title="" />
          <h2 id="breadcrumbs-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <Breadcrumbs aria-label="breadcrumb">
                      <a href="https://www.google.com/">Home</a>
                      <a href="https://www.google.com/">Products</a>
                      <a href="https://www.google.com/">Electronics</a>
                      <Typography>Smartphones</Typography>
                    </Breadcrumbs>
                  ),
                  description:
                    "Use clear, descriptive labels that represent the page hierarchy",
                },
                negative: {
                  component: (
                    <Breadcrumbs aria-label="breadcrumb">
                      <a href="https://www.google.com/">Click Here</a>
                      <a href="https://www.google.com/">Page 2</a>
                      <a href="https://www.google.com/">Link</a>
                      <Typography>Current</Typography>
                    </Breadcrumbs>
                  ),
                  description:
                    "Avoid generic labels that don't describe the actual page content",
                },
              },
              {
                positive: {
                  component: (
                    <Breadcrumbs aria-label="breadcrumb" maxItems={4}>
                      <a href="https://www.google.com/">Home</a>
                      <a href="https://www.google.com/">Level 1</a>
                      <a href="https://www.google.com/">Level 2</a>
                      <a href="https://www.google.com/">Level 3</a>
                      <a href="https://www.google.com/">Level 4</a>
                      <Typography>Current Page</Typography>
                    </Breadcrumbs>
                  ),
                  description:
                    "Use ellipsis for deep hierarchies to maintain clean layout",
                },
                negative: {
                  component: (
                    <div style={{ width: 300, overflow: "hidden" }}>
                      <Breadcrumbs aria-label="breadcrumb">
                        <a href="https://www.google.com/">Home</a>
                        <a href="https://www.google.com/">
                          Very Long Category Name
                        </a>
                        <a href="https://www.google.com/">
                          Another Long Subcategory
                        </a>
                        <a href="https://www.google.com/">Yet Another Level</a>
                        <a href="https://www.google.com/">Final Level</a>
                        <Typography>Current Page With Long Name</Typography>
                      </Breadcrumbs>
                    </div>
                  ),
                  description:
                    "Avoid overcrowding with too many items that cause layout issues",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    children: {
      control: false,
      description:
        "The content of the component, typically links and typography.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    separator: {
      control: "text",
      description:
        "Custom separator node. For example, a string or a component.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    maxItems: {
      control: "number",
      description:
        "Specifies the maximum number of breadcrumbs to display before collapsing.",
    },
    itemsBeforeCollapse: {
      control: "number",
      description:
        "If maxItems is exceeded, the number of items to show before the ellipsis.",
    },
    itemsAfterCollapse: {
      control: "number",
      description:
        "If maxItems is exceeded, the number of items to show after the ellipsis.",
    },
    ref: {
      control: false,
      description: "The ref to the root element.",
      table: {
        type: { summary: "Ref<HTMLElement>" },
      },
    },
  },
} satisfies Meta<typeof Breadcrumbs>

export default meta
type Story = StoryObj<typeof Breadcrumbs>

// --- STORIES ---

/**
 * This is the default breadcrumbs component. The last item is typically the current page
 * and is represented by a `Typography` component rather than a link.
 */
export const Default: Story = {
  args: {
    "aria-label": "breadcrumb",
    children: [
      <a key="1" href="https://www.google.com/">
        Workspace
      </a>,
      <a key="2" href="https://www.google.com/">
        Folder
      </a>,
      <Typography key="3">Board</Typography>,
    ],
  },
}

/**
 * When the number of items exceeds the `maxItems` prop, the breadcrumbs will collapse,
 * showing an ellipsis. You can control how many items are visible before and after the
 * ellipsis using `itemsBeforeCollapse` and `itemsAfterCollapse`.
 */
export const Ellipsis: Story = {
  name: "Collapsed with Ellipsis",
  args: {
    maxItems: 3,
    itemsBeforeCollapse: 1,
    itemsAfterCollapse: 1,
    "aria-label": "breadcrumb with ellipsis",
    children: [
      <a key="1" href="https://www.google.com/">
        Home
      </a>,
      <a key="2" href="https://www.google.com/">
        Products
      </a>,
      <a key="3" href="https://www.google.com/">
        Electronics
      </a>,
      <a key="4" href="https://www.google.com/">
        Mobile Phones
      </a>,
      <Typography key="5">Accessories</Typography>,
    ],
  },
}

/**
 * An example of a breadcrumb with multiple "active" items, represented by non-interactive
 * `Typography` elements. This is an uncommon use case but is supported.
 */
export const MultipleActive: Story = {
  args: {
    "aria-label": "breadcrumb with multiple active items",
    children: [
      <a key="1" href="https://www.google.com/">
        Workspace
      </a>,
      <Typography key="2" color="primary">
        Folder
      </Typography>,
      <Typography key="3">Board</Typography>,
    ],
  },
}

/**
 * You can customize the separator between items by passing a string or a React node
 * to the `separator` prop.
 */
export const CustomSeparator: Story = {
  args: {
    separator: "—",
    "aria-label": "breadcrumb with custom separator",
    children: [
      <a key="1" href="https://www.google.com/">
        Workspace
      </a>,
      <a key="2" href="https://www.google.com/">
        Folder
      </a>,
      <Typography key="3">Breadcrumbs</Typography>,
    ],
  },
}

/**
 * This is the disabled item in breadcrumbs component.
 */
export const Disabled: Story = {
  args: {
    "aria-label": "breadcrumb",
    children: [
      <a key="1" href="https://www.google.com/" aria-disabled>
        Workspace
      </a>,
      <a key="2" href="https://www.google.com/">
        Folder
      </a>,
      <IconButton key="3" disabled>
        <Heart />
      </IconButton>,
      <Typography key="4" aria-disabled>
        Board
      </Typography>,
      <Typography key="5">Group</Typography>,
    ],
  },
}

/**
 * This is the text with icon item in breadcrumbs component.
 */
export const WithIcons: Story = {
  args: {
    "aria-label": "breadcrumb",
    children: [
      <Button
        key="1"
        variant="text"
        startDecorator={<Appstore />}
        href="https://www.google.com/"
      >
        Workspace
      </Button>,
      <Button key="2" disabled variant="text" startDecorator={<Folder />}>
        Folder
      </Button>,
      <Button key="3" variant="text" startDecorator={<History />}>
        History
      </Button>,
    ],
  },
}
