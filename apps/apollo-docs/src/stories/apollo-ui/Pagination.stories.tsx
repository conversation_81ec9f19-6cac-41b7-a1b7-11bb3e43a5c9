import { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Button, Input, Pagination, Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

// meta
const meta = {
  title: "@apollo∕ui/Components/Navigation/Pagination",
  component: Pagination,
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2640-6146&m=dev",
    },
    docs: {
      description: {
        component:
          "Pagination enables the user to select a specific page from a range of pages. It provides navigation for content that is spread across multiple pages.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Pagination } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="pagination-props">Props</h2>
          <ArgTypes />
          <h2 id="pagination-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use Pagination for large datasets that would be overwhelming to display all at once",
              "Choose appropriate page sizes based on content type - typically 10-50 items per page",
              "Position pagination controls at the bottom of content, with optional top placement for long lists",
              "Use siblingCount and boundaryCount to balance navigation efficiency with visual clarity",
              "Consider using compact display type on mobile devices to save space",
              "Provide clear indication of current page and total pages for user orientation",
              "Use page size options when users might want to control how much content they see",
              "First page button and last page button are available in compact display type only",
            ]}
          />
          <h2 id="pagination-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Pagination automatically includes proper ARIA labels and roles
                for screen reader navigation and keyboard accessibility.
              </>,
              <>
                Use <code>displayType</code> prop to switch between `full` and
                `compact` display types for different screen sizes.
              </>,
              <>
                Use <code>pageSize</code> prop to provide a custom page size
                selector with <code>onPageSizeChange</code> callback. Custom
                page size options can be provided using{" "}
                <code>pageSizeOptions</code> prop. Default page size is 10.
              </>,
            ]}
          />
          <h2 id="pagination-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Pagination component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloPagination-root",
                description: "Styles applied to the pagination root container",
                usageNotes: "Use for overall pagination styling and layout",
              },
              {
                cssClassName: ".ApolloPagination-full",
                description:
                  "Styles applied to the full pagination view container",
                usageNotes: "Contains all pagination buttons in desktop view",
              },
              {
                cssClassName: ".ApolloPagination-compact",
                description:
                  "Styles applied to the compact pagination view container",
                usageNotes: "Contains simplified pagination for mobile view",
              },
              {
                cssClassName: ".ApolloPagination-prevPageButton",
                description: "Styles applied to the previous page button",
                usageNotes: "Navigation button to go to previous page",
              },
              {
                cssClassName: ".ApolloPagination-nextPageButton",
                description: "Styles applied to the next page button",
                usageNotes: "Navigation button to go to next page",
              },
              {
                cssClassName: ".ApolloPagination-firstPageButton",
                description: "Styles applied to the first page button",
                usageNotes: "Navigation button to go to first page",
              },
              {
                cssClassName: ".ApolloPagination-lastPageButton",
                description: "Styles applied to the last page button",
                usageNotes: "Navigation button to go to last page",
              },
              {
                cssClassName: ".ApolloPagination-pageInfo",
                description: "Styles applied to the page info text",
                usageNotes: "Displays current page information in compact view",
              },
              {
                cssClassName: ".ApolloPagination-pageSize",
                description:
                  "Styles applied to the page size selector container",
                usageNotes: "Contains the page size selection dropdown",
              },
            ]}
          />
          <h2 id="pagination-examples">Examples</h2>
          <Stories title="" />
          <h2 id="pagination-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                        alignItems: "center",
                      }}
                    >
                      <div style={{ width: "100%", maxWidth: "400px" }}>
                        <Typography
                          level="bodySmall"
                          style={{ color: "#666", marginBottom: 8 }}
                        >
                          Desktop view (≥768px)
                        </Typography>
                        <Pagination
                          count={15}
                          defaultPage={8}
                          displayType="full"
                          siblingCount={1}
                          boundaryCount={1}
                        />
                      </div>
                      <div style={{ width: "100%", maxWidth: "400px" }}>
                        <Typography
                          level="bodySmall"
                          style={{ color: "#666", marginBottom: 8 }}
                        >
                          Mobile view (&lt;768px)
                        </Typography>
                        <Pagination
                          count={15}
                          defaultPage={1}
                          displayType="compact"
                          pageSize={10}
                        />
                      </div>
                    </div>
                  ),
                  description:
                    "Use responsive design patterns - switch to compact display type on mobile devices (< 768px) to optimize space and maintain usability",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                        alignItems: "center",
                      }}
                    >
                      <div
                        style={{
                          width: "100%",
                          maxWidth: "400px",
                          border: "1px dashed #ccc",
                          padding: 16,
                          borderRadius: 8,
                        }}
                      >
                        <Typography
                          level="bodySmall"
                          style={{ color: "#666", marginBottom: 8 }}
                        >
                          Mobile view with full pagination
                        </Typography>
                        <Pagination
                          count={5}
                          defaultPage={3}
                          displayType="full"
                        />
                      </div>
                    </div>
                  ),
                  description:
                    "Avoid using full display type on mobile - it creates overcrowded interfaces and poor touch targets",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <div
                        style={{
                          padding: 16,
                          border: "1px solid #e0e0e0",
                          borderRadius: 8,
                          width: "100%",
                          maxWidth: "400px",
                        }}
                      >
                        <Pagination
                          count={5}
                          defaultPage={3}
                          siblingCount={1}
                          boundaryCount={1}
                        />
                      </div>
                    </div>
                  ),
                  description:
                    "Use either pagination OR infinite scroll/load more - choose one navigation pattern that fits your content and user needs",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <div
                        style={{
                          padding: 16,
                          border: "2px solid #ff6b6b",
                          borderRadius: 8,
                          width: "100%",
                          maxWidth: "400px",
                          backgroundColor: "#fff5f5",
                        }}
                      >
                        <Typography
                          level="bodyMedium"
                          style={{ marginBottom: 16, color: "#d63031" }}
                        >
                          Conflicting navigation patterns:
                        </Typography>
                        <div
                          style={{
                            marginBottom: 16,
                            display: "flex",
                            justifyContent: "center",
                          }}
                        >
                          <Pagination
                            count={5}
                            defaultPage={3}
                            siblingCount={1}
                            boundaryCount={1}
                          />
                        </div>
                        <div style={{ textAlign: "center" }}>
                          <Button variant="outline" size="small" disabled>
                            Loading more...
                          </Button>
                        </div>
                      </div>
                    </div>
                  ),
                  description:
                    "Don't combine pagination with infinite scroll - these conflicting patterns confuse users about how to navigate and can cause unexpected behavior",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  tags: ["autodocs"],
  argTypes: {
    count: { control: { type: "number", min: 1, max: 100 } },
    page: { control: { type: "number", min: 1 } },
    defaultPage: { control: { type: "number", min: 1 } },
    siblingCount: { control: { type: "number", min: 0, max: 5 } },
    boundaryCount: { control: { type: "number", min: 1, max: 5 } },
    showPrevPageButton: { control: { type: "boolean" } },
    showNextPageButton: { control: { type: "boolean" } },
    disabled: {
      control: { type: "boolean" },
      description: "Disable entire pagination",
      table: { defaultValue: { summary: "false" } },
    },
    disabledPrevPageButton: {
      control: { type: "boolean" },
      description: "Disable previous page button",
      table: { defaultValue: { summary: "false" } },
    },
    disabledNextPageButton: {
      control: { type: "boolean" },
      description: "Disable next page button",
      table: { defaultValue: { summary: "false" } },
    },

    // new props
    pageSize: {
      control: { type: "text" },
      description: "Custom ReactNode element for page size selection",
      table: { type: { summary: "ReactNode" } },
    },
    pageSizeOptions: {
      control: { type: "object" },
      description: "Options for selecting items per page",
      table: { type: { summary: "number[] | OptionProps<number>[]" } },
    },
    onPageSizeChange: {
      action: "pageSizeChanged",
      description: "Callback fired when page size changes",
      table: { type: { summary: "(value: number) => void" } },
    },
    displayType: {
      control: { type: "radio" },
      options: ["full", "compact"],
      description: "Fixed display type of pagination",
      table: { type: { summary: '"full" | "compact"' } },
    },
    showFirstPageButton: {
      control: { type: "boolean" },
      description: "Show first page button (compact display type only)",
    },
    showLastPageButton: {
      control: { type: "boolean" },
      description: "Show last page button (compact display type only)",
    },
    disabledFirstPageButton: {
      control: { type: "boolean" },
      description: "Disable first page button",
      table: { defaultValue: { summary: "false" } },
    },
    disabledLastPageButton: {
      control: { type: "boolean" },
      description: "Disable last page button",
      table: { defaultValue: { summary: "false" } },
    },
  },
} satisfies Meta<typeof Pagination>

export default meta
type Story = StoryObj<typeof meta>

/* --- Stories --- */

// 1. Basic
export const Basic: Story = {
  args: {
    count: 10,
    defaultPage: 1,
  },
}

// 2. Controlled
export const Controlled: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const totalPages = 15

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          alignItems: "center",
        }}
      >
        <div
          style={{
            padding: "16px",
            background: "#f8f9fa",
            borderRadius: "8px",
            textAlign: "center",
            minWidth: "300px",
          }}
        >
          <Typography level="titleSmall" gutterBottom>
            Search Results
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#666" }}>
            Showing page {currentPage} of {totalPages}
          </Typography>
        </div>

        <Pagination
          count={totalPages}
          page={currentPage}
          onChange={(_, page) => setCurrentPage(page)}
          siblingCount={2}
          boundaryCount={2}
        />

        <div
          style={{
            display: "flex",
            gap: "12px",
            flexWrap: "wrap",
            justifyContent: "center",
          }}
        >
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            First
          </Button>
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(Math.ceil(totalPages / 2))}
          >
            Middle
          </Button>
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
          >
            Last
          </Button>
        </div>
      </div>
    )
  },
}

// 3. Different configurations
export const DifferentConfigurations: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "32px" }}>
      <div>
        <Typography level="titleSmall">Small Dataset (5 pages)</Typography>
        <Pagination count={5} defaultPage={3} />
      </div>

      <div>
        <Typography level="titleSmall">Medium Dataset (25 pages)</Typography>
        <Pagination
          count={25}
          defaultPage={12}
          siblingCount={1}
          boundaryCount={2}
        />
      </div>

      <div>
        <Typography level="titleSmall">Large Dataset (100 pages)</Typography>
        <Pagination
          count={100}
          defaultPage={50}
          siblingCount={2}
          boundaryCount={1}
        />
      </div>

      <div>
        <Typography level="titleSmall">Extended Siblings (50 pages)</Typography>
        <Pagination
          count={50}
          defaultPage={25}
          siblingCount={3}
          boundaryCount={2}
        />
      </div>
    </div>
  ),
}

// 4. Button visibility
export const ButtonVisibility: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton
        showNextPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton={false}
        showNextPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton
        showNextPageButton={false}
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton={false}
        showNextPageButton={false}
      />
      <Pagination
        count={10}
        defaultPage={5}
        displayType="compact"
        showFirstPageButton
        showLastPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        displayType="compact"
        showFirstPageButton={false}
        showLastPageButton={false}
      />
    </div>
  ),
}

// 5. Disabled states
export const DisabledStates: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      <Pagination count={10} defaultPage={5} disabled />
      <Pagination count={10} defaultPage={5} disabledPrevPageButton />
      <Pagination count={10} defaultPage={5} disabledNextPageButton />
      <Pagination count={10} defaultPage={1} />
      <Pagination count={10} defaultPage={10} />
    </div>
  ),
}

export const DisplayTypeExample: Story = {
  render: () => {
    const [currentFull, setCurrentFull] = useState(5)
    const [currentCompact, setCurrentCompact] = useState(5)
    const totalPages = 20

    const containerStyle: React.CSSProperties = {
      display: "flex",
      flexDirection: "column",
      gap: "24px",
      alignItems: "center",
      width: "600px",
    }

    const blockStyle: React.CSSProperties = {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "8px",
      padding: "16px",
      border: "1px solid #ddd",
      borderRadius: "8px",
      width: "100%",
    }

    return (
      <div style={containerStyle}>
        <div style={blockStyle}>
          <Typography level="titleSmall">Responsive Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentFull}
            onChange={(_, page) => setCurrentFull(page)}
            siblingCount={2}
            boundaryCount={1}
          />
        </div>
        <div style={blockStyle}>
          <Typography level="titleSmall">Full Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentFull}
            onChange={(_, page) => setCurrentFull(page)}
            siblingCount={2}
            boundaryCount={1}
            displayType="full"
          />
        </div>

        <div style={blockStyle}>
          <Typography level="titleSmall">Compact Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentCompact}
            onChange={(_, page) => setCurrentCompact(page)}
            displayType="compact"
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates Pagination in both `full` and `compact` display types.",
      },
    },
  },
}

// 6. Table pagination example with pageSize
export const TablePaginationExample: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const [itemsPerPage, setItemsPerPage] = useState(10)
    const totalItems = 247

    return (
      <div style={{ width: "800px" }}>
        <Pagination
          count={totalItems}
          page={currentPage}
          onChange={(_, page) => setCurrentPage(page)}
          siblingCount={1}
          boundaryCount={1}
          pageSize={itemsPerPage}
          pageSizeOptions={[5, 10, 25, 50]}
          onPageSizeChange={(size) => {
            setItemsPerPage(size)
            setCurrentPage(1)
          }}
        />
      </div>
    )
  },
}

// 7. Real-world search results example
export const SearchResultsExample: Story = {
  render: () => {
    const [searchQuery, setSearchQuery] = useState(
      "react components design system"
    )
    const [currentPage, setCurrentPage] = useState(1)
    const [itemsPerPage, setItemsPerPage] = useState(10)
    const [isLoading, setIsLoading] = useState(false)
    const [searchTime, setSearchTime] = useState(0.42)
    const [showSuggestions, setShowSuggestions] = useState(false)

    // Mock search results data
    const mockResults = [
      {
        id: 1,
        title: "Building Scalable React Component Libraries",
        url: "https://example.com/react-component-libraries",
        snippet:
          "Learn how to create reusable, maintainable React components that scale across large applications. This comprehensive guide covers design patterns, TypeScript integration, and testing strategies.",
        type: "Article",
        date: "2024-01-15",
        author: "Sarah Chen",
        tags: ["React", "Components", "TypeScript"],
        saved: false,
      },
      {
        id: 2,
        title: "Design System Component Architecture",
        url: "https://example.com/design-system-architecture",
        snippet:
          "Explore the fundamental principles of design system architecture, including token-based design, component composition, and cross-platform consistency.",
        type: "Guide",
        date: "2024-01-10",
        author: "Alex Rodriguez",
        tags: ["Design Systems", "Architecture", "Tokens"],
        saved: true,
      },
      {
        id: 3,
        title: "React Component Testing Best Practices",
        url: "https://example.com/component-testing",
        snippet:
          "Master the art of testing React components with Jest, React Testing Library, and modern testing patterns. Includes examples for hooks, context, and async components.",
        type: "Tutorial",
        date: "2024-01-08",
        author: "Michael Kim",
        tags: ["Testing", "Jest", "React Testing Library"],
        saved: false,
      },
      {
        id: 4,
        title: "Apollo Design System Documentation",
        url: "https://example.com/apollo-design-system",
        snippet:
          "Complete documentation for the Apollo Design System, featuring comprehensive component APIs, usage guidelines, and implementation examples.",
        type: "Documentation",
        date: "2024-01-05",
        author: "Apollo Team",
        tags: ["Apollo", "Documentation", "Components"],
        saved: false,
      },
      {
        id: 5,
        title: "Component Performance Optimization",
        url: "https://example.com/component-performance",
        snippet:
          "Optimize React component performance with memoization, lazy loading, and efficient rendering patterns. Includes real-world performance metrics and case studies.",
        type: "Article",
        date: "2024-01-03",
        author: "Emma Thompson",
        tags: ["Performance", "Optimization", "React"],
        saved: true,
      },
      {
        id: 6,
        title: "Building Scalable React Component Libraries",
        url: "https://example.com/react-component-libraries",
        snippet:
          "Learn how to create reusable, maintainable React components that scale across large applications. This comprehensive guide covers design patterns, TypeScript integration, and testing strategies.",
        type: "Article",
        date: "2024-01-02",
        author: "John Doe",
        tags: ["React", "Components", "Libraries"],
        saved: false,
      },
      {
        id: 7,
        title: "Designing for Accessibility in React",
        url: "https://example.com/react-accessibility",
        snippet:
          "Explore best practices for designing accessible React components. Learn about ARIA attributes, keyboard navigation, and color contrast guidelines.",
        type: "Article",
        date: "2024-01-01",
        author: "Sarah Chen",
        tags: ["Accessibility", "React", "Design"],
        saved: false,
      },
      {
        id: 8,
        title: "Apollo UI Components: The Ultimate Guide",
        url: "https://example.com/apollo-ui-components",
        snippet:
          "Discover the power of Apollo UI Components with this comprehensive guide. Learn how to integrate Apollo's pre-built components into your React applications for faster development and consistent design.",
        type: "Guide",
        date: "2023-12-31",
        author: "Apollo Team",
        tags: ["Apollo", "UI Components", "Guide"],
        saved: false,
      },
      {
        id: 9,
        title: "Advanced React Testing Techniques",
        url: "https://example.com/react-testing-techniques",
        snippet:
          "Dive deep into advanced React testing techniques using Jest and React Testing Library. Explore strategies for testing complex components, hooks, and asynchronous behavior.",
        type: "Tutorial",
        date: "2023-12-30",
        author: "Michael Kim",
        tags: ["Testing", "Jest", "React Testing Library"],
        saved: false,
      },
      {
        id: 10,
        title: "The Future of Design Systems",
        url: "https://example.com/future-of-design-systems",
        snippet:
          "Explore the latest trends and innovations in design systems. Learn about token-based design, component libraries, and the future of design in software development.",
        type: "Article",
        date: "2023-12-29",
        author: "Alex Rodriguez",
        tags: ["Design Systems", "Future", "Trends"],
        saved: false,
      },
      {
        id: 11,
        title: "Building Scalable React Component Libraries",
        url: "https://example.com/react-component-libraries",
        snippet:
          "Learn how to create reusable, maintainable React components that scale across large applications. This comprehensive guide covers design patterns, TypeScript integration, and testing strategies.",
        type: "Article",
        date: "2023-12-28",
        author: "John Doe",
        tags: ["React", "Components", "Libraries"],
        saved: false,
      },
      {
        id: 12,
        title: "Designing for Accessibility in React",
        url: "https://example.com/react-accessibility",
        snippet:
          "Explore best practices for designing accessible React components. Learn about ARIA attributes, keyboard navigation, and color contrast guidelines.",
        type: "Article",
        date: "2023-12-27",
        author: "Sarah Chen",
        tags: ["Accessibility", "React", "Design"],
        saved: false,
      },
    ]

    const searchSuggestions = [
      "react component library",
      "design system patterns",
      "component testing strategies",
      "typescript react components",
      "apollo ui components",
    ]
    
    const totalResults = searchQuery.trim() === "" ? 0 : 1247
    const totalPages = Math.ceil(totalResults / itemsPerPage)

    const handleSearch = (query: string) => {
      if (query.trim() === "") return
      setIsLoading(true)
      setSearchQuery(query)
      setCurrentPage(1)
      setShowSuggestions(false)
      // Simulate API call
      setTimeout(() => {
        setIsLoading(false)
        setSearchTime(Math.random() * 0.5 + 0.2)
      }, 800)
    }

    const handlePageChange = (event: any, page: number) => {
      setIsLoading(true)
      setCurrentPage(page)
      window.scrollTo({ top: 0, behavior: "smooth" })
      // Simulate API call
      setTimeout(() => setIsLoading(false), 400)
    }

    const handlePageSizeChange = (size: number) => {
      setIsLoading(true)
      setItemsPerPage(size)
      setCurrentPage(1)
      setTimeout(() => setIsLoading(false), 400)
    }

    const toggleSave = (id: number) => {
      // Mock save functionality
      console.log(`Toggled save for result ${id}`)
    }

    const openInNewTab = (url: string) => {
      window.open(url, "_blank")
    }

    return (
      <div
        style={{
          width: "100%",
          margin: "0 auto",
          fontFamily: "system-ui, -apple-system, sans-serif",
        }}
      >
        {/* Search Header */}
        <div
          style={{
            marginBottom: "24px",
            padding: "20px",
            backgroundColor: "#f8f9fa",
            borderRadius: "12px",
          }}
        >
          <div style={{ position: "relative", marginBottom: "16px" }}>
            <div style={{ display: "flex", gap: "12px" }}>
              <div style={{ position: "relative", flex: 1 }}>
                <Input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value)
                    setShowSuggestions(e.target.value.length > 2)
                  }}
                  onKeyPress={(e) =>
                    e.key === "Enter" && handleSearch(searchQuery)
                  }
                  onFocus={() => setShowSuggestions(searchQuery.length > 2)}
                  onBlur={() =>
                    setTimeout(() => setShowSuggestions(false), 200)
                  }
                  placeholder="Search for components, tutorials, documentation..."
                  fullWidth
                />
                {showSuggestions && (
                  <div
                    style={{
                      position: "absolute",
                      top: "100%",
                      left: 0,
                      right: 0,
                      backgroundColor: "white",
                      border: "1px solid #e1e5e9",
                      borderRadius: "8px",
                      boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                      zIndex: 10,
                      marginTop: "4px",
                    }}
                  >
                    {searchSuggestions.map((suggestion, index) => (
                      <div
                        key={index}
                        style={{
                          padding: "12px 16px",
                          cursor: "pointer",
                          borderBottom:
                            index < searchSuggestions.length - 1
                              ? "1px solid #f0f0f0"
                              : "none",
                        }}
                        onMouseDown={() => handleSearch(suggestion)}
                      >
                        <Typography level="bodyMedium">{suggestion}</Typography>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <Button
                variant="filled"
                onClick={() => handleSearch(searchQuery)}
                disabled={isLoading || searchQuery.trim() === ""}
              >
                {isLoading ? "Searching..." : "Search"}
              </Button>
            </div>
          </div>
        </div>

        {/* Empty State */}
        {totalResults === 0 && searchQuery.trim() !== "" && !isLoading && (
          <div
            style={{
              textAlign: "center",
              padding: "60px 20px",
              backgroundColor: "#f8f9fa",
              borderRadius: "12px",
              marginBottom: "24px",
            }}
          >
            <Typography
              level="titleMedium"
              style={{ marginBottom: "12px", color: "#666" }}
            >
              No results found for "{searchQuery}"
            </Typography>
            <Typography
              level="bodyMedium"
              style={{ color: "#888", marginBottom: "20px" }}
            >
              Try different keywords or check your spelling
            </Typography>
            <div
              style={{
                display: "flex",
                gap: "8px",
                justifyContent: "center",
                flexWrap: "wrap",
              }}
            >
              <Typography level="bodySmall" style={{ color: "#666" }}>
                Suggestions:
              </Typography>
              {searchSuggestions.slice(0, 3).map((suggestion, index) => (
                <Button
                  key={index}
                  variant="text"
                  size="small"
                  onClick={() => handleSearch(suggestion)}
                  style={{ fontSize: "14px", padding: "4px 8px" }}
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Search Results */}
        {totalResults > 0 && (
          <div style={{ marginBottom: "32px", width: "100%", }}>
            {isLoading ? (
              <div style={{ padding: "40px", textAlign: "center", width: "100%", }}>
                <div
                  style={{
                    display: "inline-block",
                    width: "40px",
                    height: "40px",
                    border: "4px solid #f3f3f3",
                    borderTop: "4px solid #007bff",
                    borderRadius: "50%",
                    animation: "spin 1s linear infinite",
                    marginBottom: "16px",
                  }}
                />
                <Typography level="bodyMedium" style={{ color: "#666" }}>
                  Loading search results...
                </Typography>
              </div>
            ) : (
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "24px",
                  width: "100%",
                }}
              >
                {mockResults.map((result) => (
                  <div
                    key={result.id}
                    style={{
                      padding: "24px",
                      border: "1px solid #e1e5e9",
                      borderRadius: "12px",
                      backgroundColor: "#fff",
                      boxShadow: "0 2px 4px rgba(0,0,0,0.04)",
                      transition: "box-shadow 0.2s, border-color 0.2s",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.boxShadow =
                        "0 4px 12px rgba(0,0,0,0.08)"
                      e.currentTarget.style.borderColor = "#c1c7cd"
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.boxShadow =
                        "0 2px 4px rgba(0,0,0,0.04)"
                      e.currentTarget.style.borderColor = "#e1e5e9"
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                        marginBottom: "8px",
                      }}
                    >
                      <div style={{ flex: 1 }}>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "8px",
                            marginBottom: "4px",
                          }}
                        >
                          <Typography
                            level="titleMedium"
                            style={{
                              color: "#1a73e8",
                              cursor: "pointer",
                              textDecoration: "none",
                            }}
                            onClick={() => openInNewTab(result.url)}
                          >
                            {result.title}
                          </Typography>
                          <span
                            style={{
                              padding: "2px 8px",
                              backgroundColor: "#e8f0fe",
                              color: "#1a73e8",
                              borderRadius: "12px",
                              fontSize: "12px",
                              fontWeight: 500,
                            }}
                          >
                            {result.type}
                          </span>
                        </div>
                        <Typography
                          level="bodySmall"
                          style={{ color: "#5f6368", marginBottom: "8px" }}
                        >
                          {result.url}
                        </Typography>
                        <Typography
                          level="bodyMedium"
                          style={{
                            color: "#3c4043",
                            lineHeight: "1.5",
                            marginBottom: "12px",
                          }}
                        >
                          {result.snippet}
                        </Typography>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "16px",
                            flexWrap: "wrap",
                          }}
                        >
                          <Typography
                            level="bodySmall"
                            style={{ color: "#666" }}
                          >
                            By {result.author} •{" "}
                            {new Date(result.date).toLocaleDateString()}
                          </Typography>
                          <div style={{ display: "flex", gap: "6px" }}>
                            {result.tags.map((tag, index) => (
                              <span
                                key={index}
                                style={{
                                  padding: "2px 6px",
                                  backgroundColor: "#f1f3f4",
                                  color: "#5f6368",
                                  borderRadius: "8px",
                                  fontSize: "11px",
                                }}
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div
                        style={{
                          display: "flex",
                          gap: "8px",
                          marginLeft: "16px",
                        }}
                      >
                        <Button
                          variant="text"
                          size="small"
                          onClick={() => toggleSave(result.id)}
                          style={{
                            minWidth: "auto",
                            padding: "6px 12px",
                            color: result.saved ? "#1a73e8" : "#666",
                            backgroundColor: result.saved
                              ? "#e8f0fe"
                              : "transparent",
                          }}
                        >
                          {result.saved ? "Saved" : "Save"}
                        </Button>
                        <Button
                          variant="text"
                          size="small"
                          onClick={() => openInNewTab(result.url)}
                          style={{ minWidth: "auto", padding: "6px 12px" }}
                        >
                          Open ↗
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Pagination */}
        {totalResults > 0 && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              padding: "32px 0",
              borderTop: "1px solid #e1e5e9",
              backgroundColor: "#f8f9fa",
              borderRadius: "12px",
              marginTop: "24px",
            }}
          >
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={handlePageChange}
              siblingCount={2}
              boundaryCount={1}
              pageSize={itemsPerPage}
              pageSizeOptions={[5, 10, 20]}
              onPageSizeChange={handlePageSizeChange}
              disabled={isLoading}
            />
          </div>
        )}

        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Comprehensive real-world search interface with search input, filters, loading states, result cards, empty states, search suggestions, result actions, and full pagination functionality. Demonstrates best practices for search UX and state management.",
      },
    },
  },
}
