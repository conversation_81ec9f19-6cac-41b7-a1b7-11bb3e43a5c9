import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>,  Typography } from "@apollo/ui"
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"

// meta
const meta = {
  title: "@apollo∕ui/Components/Navigation/Pagination",
  component: Pagination,
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2640-6146&m=dev",
    },
    docs: {
      description: {
        component:
          "Pagination enables the user to select a specific page from a range of pages. It provides navigation for content that is spread across multiple pages.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Pagination } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="pagination-props">Props</h2>
          <ArgTypes />
          <h2 id="pagination-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use Pagination for large datasets that would be overwhelming to display all at once",
              "Choose appropriate page sizes based on content type - typically 10-50 items per page",
              "Position pagination controls at the bottom of content, with optional top placement for long lists",
              "Use siblingCount and boundaryCount to balance navigation efficiency with visual clarity",
              "Consider using compact display type on mobile devices to save space",
              "Provide clear indication of current page and total pages for user orientation",
              "Use page size options when users might want to control how much content they see",
              "First page button and last page button are available in compact display type only",
            ]}
          />
          <h2 id="pagination-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Pagination automatically includes proper ARIA labels and roles for
                screen reader navigation and keyboard accessibility.
              </>,
              <>
                Use <code>displayType</code> prop to switch between `full` and `compact` display types for different screen sizes.
              </>,
              <>
                Use <code>pageSize</code> prop to provide a custom page size selector with <code>onPageSizeChange</code> callback.
                Custom page size options can be provided using <code>pageSizeOptions</code> prop. Default page size is 10.
              </>
            ]}
          />
          <h2 id="pagination-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Pagination component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloPagination-root",
                description: "Styles applied to the pagination root container",
                usageNotes: "Use for overall pagination styling and layout",
              },
              {
                cssClassName: ".ApolloPagination-full",
                description: "Styles applied to the full pagination view container",
                usageNotes: "Contains all pagination buttons in desktop view",
              },
              {
                cssClassName: ".ApolloPagination-compact",
                description: "Styles applied to the compact pagination view container",
                usageNotes: "Contains simplified pagination for mobile view",
              },
              {
                cssClassName: ".ApolloPagination-prevPageButton",
                description: "Styles applied to the previous page button",
                usageNotes: "Navigation button to go to previous page",
              },
              {
                cssClassName: ".ApolloPagination-nextPageButton",
                description: "Styles applied to the next page button",
                usageNotes: "Navigation button to go to next page",
              },
              {
                cssClassName: ".ApolloPagination-firstPageButton",
                description: "Styles applied to the first page button",
                usageNotes: "Navigation button to go to first page",
              },
              {
                cssClassName: ".ApolloPagination-lastPageButton",
                description: "Styles applied to the last page button",
                usageNotes: "Navigation button to go to last page",
              },
              {
                cssClassName: ".ApolloPagination-pageInfo",
                description: "Styles applied to the page info text",
                usageNotes: "Displays current page information in compact view",
              },
              {
                cssClassName: ".ApolloPagination-pageSize",
                description: "Styles applied to the page size selector container",
                usageNotes: "Contains the page size selection dropdown",
              },
            ]}
          />
           <h2 id="pagination-examples">Examples</h2>
          <Stories title="" />
          <h2 id="pagination-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 16, alignItems: "center" }}>
                      <div style={{ width: "100%", maxWidth: "400px" }}>
                        <Typography level="bodySmall" style={{ color: "#666", marginBottom: 8 }}>
                          Desktop view (≥768px)
                        </Typography>
                        <Pagination
                          count={15}
                          defaultPage={8}
                          displayType="full"
                          siblingCount={1}
                          boundaryCount={1}
                        />
                      </div>
                      <div style={{ width: "100%", maxWidth: "400px" }}>
                        <Typography level="bodySmall" style={{ color: "#666", marginBottom: 8 }}>
                          Mobile view (&lt;768px)
                        </Typography>
                        <Pagination
                          count={15}
                          defaultPage={1}
                          displayType="compact"
                          pageSize={10}
                        />
                      </div>
                    </div>
                  ),
                  description: "Use responsive design patterns - switch to compact display type on mobile devices (< 768px) to optimize space and maintain usability",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 16, alignItems: "center" }}>
                      <div style={{ width: "100%", maxWidth: "400px", border: "1px dashed #ccc", padding: 16, borderRadius: 8 }}>
                        <Typography level="bodySmall" style={{ color: "#666", marginBottom: 8 }}>
                          Mobile view with full pagination
                        </Typography>
                        <Pagination
                          count={5}
                          defaultPage={3}
                          displayType="full"
                        />
                      </div>
                    </div>
                  ),
                  description: "Avoid using full display type on mobile - it creates overcrowded interfaces and poor touch targets",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 16, alignItems: "center", width: "100%" }}>
                      <div style={{ padding: 16, border: "1px solid #e0e0e0", borderRadius: 8, width: "100%", maxWidth: "400px" }}>
                          <Pagination
                            count={5}
                            defaultPage={3}
                            siblingCount={1}
                            boundaryCount={1}
                          />
                      </div>
                    </div>
                  ),
                  description: "Use either pagination OR infinite scroll/load more - choose one navigation pattern that fits your content and user needs",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", flexDirection: "column", gap: 16, alignItems: "center", width: "100%" }}>
                      <div style={{ padding: 16, border: "2px solid #ff6b6b", borderRadius: 8, width: "100%", maxWidth: "400px", backgroundColor: "#fff5f5" }}>
                        <Typography level="bodyMedium" style={{ marginBottom: 16, color: "#d63031" }}>
                          Conflicting navigation patterns:
                        </Typography>
                        <div style={{ marginBottom: 16, display: "flex", justifyContent: "center" }}>
                          <Pagination
                            count={5}
                            defaultPage={3}
                            siblingCount={1}
                            boundaryCount={1}
                          />
                        </div>
                        <div style={{ textAlign: "center" }}>
                          <Button variant="outline" size="small" disabled>
                            Loading more...
                          </Button>
                        </div>
                      </div>
                    </div>
                  ),
                  description: "Don't combine pagination with infinite scroll - these conflicting patterns confuse users about how to navigate and can cause unexpected behavior",
                },
              },
            ]}
          />
         
        </>
      ),
    },
  },
  tags: ["autodocs"],
  argTypes: {
    count: { control: { type: "number", min: 1, max: 100 } },
    page: { control: { type: "number", min: 1 } },
    defaultPage: { control: { type: "number", min: 1 } },
    siblingCount: { control: { type: "number", min: 0, max: 5 } },
    boundaryCount: { control: { type: "number", min: 1, max: 5 } },
    showPrevPageButton: { control: { type: "boolean" } },
    showNextPageButton: { control: { type: "boolean" } },
    disabled: { control: { type: "boolean" }, description: "Disable entire pagination", table: { defaultValue: { summary: "false" } } },
    disabledPrevPageButton: { control: { type: "boolean" }, description: "Disable previous page button", table: { defaultValue: { summary: "false" } } },
    disabledNextPageButton: { control: { type: "boolean" }, description: "Disable next page button", table: { defaultValue: { summary: "false" } } },

    // new props
    pageSize: {
      control: { type: "text" },
      description: "Custom ReactNode element for page size selection",
      table: { type: { summary: "ReactNode" } },
    },
    pageSizeOptions: {
      control: { type: "object" },
      description: "Options for selecting items per page",
      table: { type: { summary: "number[] | OptionProps<number>[]" } },
    },
    onPageSizeChange: {
      action: "pageSizeChanged",
      description: "Callback fired when page size changes",
      table: { type: { summary: "(value: number) => void" } },
    },
    displayType: {
      control: { type: "radio" },
      options: ["full", "compact"],
      description: "Fixed display type of pagination",
      table: { type: { summary: '"full" | "compact"' } },
    },
    showFirstPageButton: { control: { type: "boolean" }, description: "Show first page button (compact display type only)" },
    showLastPageButton: { control: { type: "boolean" }, description: "Show last page button (compact display type only)" },
    disabledFirstPageButton: { control: { type: "boolean" }, description: "Disable first page button", table: { defaultValue: { summary: "false" } } },
    disabledLastPageButton: { control: { type: "boolean" }, description: "Disable last page button", table: { defaultValue: { summary: "false" } } },
  },
} satisfies Meta<typeof Pagination>

export default meta
type Story = StoryObj<typeof meta>

/* --- Stories --- */

// 1. Basic
export const Basic: Story = {
  args: {
    count: 10,
    defaultPage: 1,
  },
}

// 2. Controlled
export const Controlled: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const totalPages = 15

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          alignItems: "center",
        }}
      >
        <div
          style={{
            padding: "16px",
            background: "#f8f9fa",
            borderRadius: "8px",
            textAlign: "center",
            minWidth: "300px",
          }}
        >
          <Typography level="titleSmall" gutterBottom>
            Search Results
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#666" }}>
            Showing page {currentPage} of {totalPages}
          </Typography>
        </div>

        <Pagination
          count={totalPages}
          page={currentPage}
          onChange={(_, page) => setCurrentPage(page)}
          siblingCount={2}
          boundaryCount={2}
        />

        <div
          style={{
            display: "flex",
            gap: "12px",
            flexWrap: "wrap",
            justifyContent: "center",
          }}
        >
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            First
          </Button>
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(Math.ceil(totalPages / 2))}
          >
            Middle
          </Button>
          <Button
            size="small"
            variant="outline"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
          >
            Last
          </Button>
        </div>
      </div>
    )
  },
}

// 3. Different configurations
export const DifferentConfigurations: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "32px" }}>
      <div>
        <Typography level="titleSmall">Small Dataset (5 pages)</Typography>
        <Pagination count={5} defaultPage={3} />
      </div>

      <div>
        <Typography level="titleSmall">Medium Dataset (25 pages)</Typography>
        <Pagination
          count={25}
          defaultPage={12}
          siblingCount={1}
          boundaryCount={2}
        />
      </div>

      <div>
        <Typography level="titleSmall">Large Dataset (100 pages)</Typography>
        <Pagination
          count={100}
          defaultPage={50}
          siblingCount={2}
          boundaryCount={1}
        />
      </div>

      <div>
        <Typography level="titleSmall">Extended Siblings (50 pages)</Typography>
        <Pagination
          count={50}
          defaultPage={25}
          siblingCount={3}
          boundaryCount={2}
        />
      </div>
    </div>
  ),
}

// 4. Button visibility
export const ButtonVisibility: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton
        showNextPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton={false}
        showNextPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton
        showNextPageButton={false}
      />
      <Pagination
        count={10}
        defaultPage={5}
        showPrevPageButton={false}
        showNextPageButton={false}
      />
      <Pagination
        count={10}
        defaultPage={5}
        displayType="compact"
        showFirstPageButton
        showLastPageButton
      />
      <Pagination
        count={10}
        defaultPage={5}
        displayType="compact"
        showFirstPageButton={false}
        showLastPageButton={false}
      />
    </div>
  ),
}

// 5. Disabled states
export const DisabledStates: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
      <Pagination count={10} defaultPage={5} disabled />
      <Pagination count={10} defaultPage={5} disabledPrevPageButton />
      <Pagination count={10} defaultPage={5} disabledNextPageButton />
      <Pagination count={10} defaultPage={1} />
      <Pagination count={10} defaultPage={10} />
    </div>
  ),
}

export const DisplayTypeExample: Story = {
  render: () => {
    const [currentFull, setCurrentFull] = useState(5)
    const [currentCompact, setCurrentCompact] = useState(5)
    const totalPages = 20

    const containerStyle: React.CSSProperties = {
      display: "flex",
      flexDirection: "column",
      gap: "24px",
      alignItems: "center",
      width: "600px",
    }

    const blockStyle: React.CSSProperties = {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: "8px",
      padding: "16px",
      border: "1px solid #ddd",
      borderRadius: "8px",
      width: "100%",
    }

    return (
      <div style={containerStyle}>
        <div style={blockStyle}>
          <Typography level="titleSmall">Responsive Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentFull}
            onChange={(_, page) => setCurrentFull(page)}
            siblingCount={2}
            boundaryCount={1}
          />
        </div>
        <div style={blockStyle}>
          <Typography level="titleSmall">Full Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentFull}
            onChange={(_, page) => setCurrentFull(page)}
            siblingCount={2}
            boundaryCount={1}
            displayType="full"
          />
        </div>

        <div style={blockStyle}>
          <Typography level="titleSmall">Compact Display Type</Typography>
          <Pagination
            count={totalPages}
            page={currentCompact}
            onChange={(_, page) => setCurrentCompact(page)}
            displayType="compact"
          />
        </div>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates Pagination in both `full` and `compact` display types.",
      },
    },
  },
}

// 6. Table pagination example with pageSize
export const TablePaginationExample: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const [itemsPerPage, setItemsPerPage] = useState(10)
    const totalItems = 247

    return (
      <div style={{ width: "800px" }}>
        <Pagination
          count={totalItems}
          page={currentPage}
          onChange={(_, page) => setCurrentPage(page)}
          siblingCount={1}
          boundaryCount={1}
          pageSize={itemsPerPage}
          pageSizeOptions={[5, 10, 25, 50]}
          onPageSizeChange={(size) => {
            setItemsPerPage(size)
            setCurrentPage(1)
          }}
        />
      </div>
    )
  },
}

// 7. Search results example
export const SearchResultsExample: Story = {
  render: () => {
    const [currentPage, setCurrentPage] = useState(1)
    const totalResults = 1247
    const resultsPerPage = 20
    const totalPages = Math.ceil(totalResults / resultsPerPage)

    const startResult = (currentPage - 1) * resultsPerPage + 1
    const endResult = Math.min(currentPage * resultsPerPage, totalResults)

    return (
      <div style={{ width: "700px" }}>
        <Typography level="titleLarge">Search Results</Typography>
        <Typography
          level="bodySmall"
          style={{ color: "#666", marginBottom: "24px" }}
        >
          About {totalResults.toLocaleString()} results
        </Typography>

        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "16px",
            padding: "20px",
            background: "#fafafa",
            borderRadius: "8px",
          }}
        >
          <Typography level="bodyMedium" style={{ color: "#666" }}>
            Page {currentPage} of {totalPages} (showing {startResult}-
            {endResult})
          </Typography>

          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(_, page) => setCurrentPage(page)}
            siblingCount={2}
            boundaryCount={1}
          />

          <div style={{ display: "flex", gap: "16px" }}>
            <Button
              variant="text"
              size="small"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 10))}
              disabled={currentPage <= 10}
            >
              ← Previous 10
            </Button>
            <Button
              variant="text"
              size="small"
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 10))
              }
              disabled={currentPage > totalPages - 10}
            >
              Next 10 →
            </Button>
          </div>
        </div>
      </div>
    )
  },
}
